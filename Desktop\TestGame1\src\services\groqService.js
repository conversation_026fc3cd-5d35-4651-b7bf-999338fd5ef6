import Groq from 'groq-sdk'

// Initialize Groq client
const groq = new Groq({
  apiKey: import.meta.env.VITE_GROQ_API_KEY,
  dangerouslyAllowBrowser: true // Note: In production, use a backend proxy
})

const GENIE_SYSTEM_PROMPT = `You are a mischievous but entertaining AI Genie in a "3 Wishes" game. Your personality traits:

PERSONALITY:
- Dramatic and theatrical in speech
- Witty and clever with wordplay
- Slightly sarcastic but not mean-spirited
- Ancient wisdom with modern humor
- Enjoys finding creative loopholes in wishes

CORE RULES:
1. ALWAYS grant the wish, but with an unexpected twist, catch, or ironic consequence
2. Be creative and humorous, not cruel or devastating
3. Use mystical language and dramatic flair
4. Keep responses engaging and entertaining
5. The consequence should be clever and make the player think "I should have been more specific!"

RESPONSE FORMAT:
- Start with dramatic acknowledgment
- Grant the wish with flair
- Reveal the twist/consequence
- End with a hint about remaining wishes (if any)
- Use emojis and formatting for dramatic effect

EXAMPLE PATTERNS:
- Literal interpretations (wish to fly → can only fly 2 inches high)
- Unintended side effects (million dollars → it's counterfeit)
- <PERSON>'s paw style twists (fame → infamous for wrong reasons)
- Time/location/context changes (rich → but in a video game)

Keep responses under 200 words but make them memorable and entertaining!`

export const processWish = async (wishText, wishesRemaining) => {
  try {
    const wishNumber = 4 - wishesRemaining
    const isLastWish = wishesRemaining === 1

    const userPrompt = `WISH #${wishNumber}: "${wishText}"

${isLastWish ? 'This is their FINAL wish - make it extra dramatic!' : `They have ${wishesRemaining - 1} wishes remaining after this.`}

Grant this wish with a creative twist or unexpected consequence. Be entertaining and clever!`

    const completion = await groq.chat.completions.create({
      messages: [
        {
          role: "system",
          content: GENIE_SYSTEM_PROMPT
        },
        {
          role: "user",
          content: userPrompt
        }
      ],
      model: "llama3-70b-8192",
      temperature: 0.8,
      max_tokens: 400,
      top_p: 0.9,
      stream: false
    })

    return completion.choices[0]?.message?.content || "🧞‍♂️ *The genie seems confused by your wish and scratches his mystical head* Perhaps try rephrasing that, mortal?"

  } catch (error) {
    console.error('Groq API Error:', error)
    
    // Fallback responses for different error types
    if (error.message?.includes('API key')) {
      throw new Error('Invalid API key - please check your Groq API configuration')
    } else if (error.message?.includes('rate limit')) {
      throw new Error('Too many wishes too quickly! Even genies need a moment to rest.')
    } else {
      throw new Error('The mystical energies are disrupted. Please try again!')
    }
  }
}

// Utility function to validate API key
export const validateApiKey = () => {
  const apiKey = import.meta.env.VITE_GROQ_API_KEY
  return apiKey && apiKey.startsWith('gsk_')
}

// Test function for API connectivity
export const testGroqConnection = async () => {
  try {
    const completion = await groq.chat.completions.create({
      messages: [
        {
          role: "user",
          content: "Say 'Connection successful!' if you can hear me."
        }
      ],
      model: "llama3-70b-8192",
      max_tokens: 10
    })
    
    return completion.choices[0]?.message?.content || 'Test failed'
  } catch (error) {
    throw error
  }
}
