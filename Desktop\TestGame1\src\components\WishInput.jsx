import React, { useState } from 'react'

const WishInput = ({ onWish, disabled, wishesRemaining }) => {
  const [wishText, setWishText] = useState('')

  const handleSubmit = (e) => {
    e.preventDefault()
    if (wishText.trim() && !disabled) {
      onWish(wishText.trim())
      setWishText('')
    }
  }

  const handleKeyPress = (e) => {
    if (e.key === 'Enter' && !e.shiftKey) {
      e.preventDefault()
      handleSubmit(e)
    }
  }

  const getPlaceholderText = () => {
    switch (wishesRemaining) {
      case 3:
        return "Make your first wish... (e.g., 'I wish for a million dollars')"
      case 2:
        return "Your second wish awaits..."
      case 1:
        return "Choose your final wish carefully..."
      default:
        return "No wishes remaining"
    }
  }

  return (
    <div className="wish-input-container">
      <form onSubmit={handleSubmit} className="wish-form">
        <div className="input-group">
          <textarea
            value={wishText}
            onChange={(e) => setWishText(e.target.value)}
            onKeyPress={handleKeyPress}
            placeholder={getPlaceholderText()}
            disabled={disabled}
            className="wish-textarea"
            rows="3"
            maxLength="500"
          />
          <button 
            type="submit" 
            disabled={disabled || !wishText.trim()}
            className="wish-button"
          >
            {disabled ? '✨ Processing...' : '🧞‍♂️ Make Wish'}
          </button>
        </div>
        <div className="input-footer">
          <span className="character-count">
            {wishText.length}/500 characters
          </span>
          <span className="hint">
            Press Enter to submit, Shift+Enter for new line
          </span>
        </div>
      </form>
    </div>
  )
}

export default WishInput
