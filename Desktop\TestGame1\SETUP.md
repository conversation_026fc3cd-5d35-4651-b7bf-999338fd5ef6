# 3 Wishes AI Genie Game - Setup Guide

## 🚀 Quick Start

### Prerequisites
1. **Node.js** (version 16 or higher) - Download from [nodejs.org](https://nodejs.org/)
2. **Groq API Key** - Get one free from [console.groq.com](https://console.groq.com/keys)

### Installation Steps

1. **Install Dependencies**
   ```bash
   npm install
   ```

2. **Set up Environment Variables**
   ```bash
   # Copy the example environment file
   copy .env.example .env
   
   # Edit .env and add your Groq API key
   # VITE_GROQ_API_KEY=your_actual_api_key_here
   ```

3. **Start the Development Server**
   ```bash
   npm run dev
   ```

4. **Open Your Browser**
   - The game will automatically open at `http://localhost:3000`
   - If it doesn't open automatically, navigate there manually

## 🔑 Getting Your Groq API Key

1. Visit [console.groq.com](https://console.groq.com/)
2. Sign up for a free account
3. Go to the API Keys section
4. Create a new API key
5. Copy the key (it starts with `gsk_`)
6. Paste it in your `.env` file

## 🎮 How to Play

1. **Meet the Genie**: The AI genie will introduce itself
2. **Make Your Wishes**: Type your wishes in the text area
3. **Enjoy the Consequences**: Watch as the genie grants your wishes with unexpected twists!
4. **Three Wishes Only**: You get exactly 3 wishes, so choose wisely!

## 🛠️ Project Structure

```
3-wishes-ai-genie/
├── src/
│   ├── components/
│   │   ├── GenieChat.jsx      # Chat interface
│   │   ├── WishInput.jsx      # Wish input form
│   │   └── WishCounter.jsx    # Remaining wishes display
│   ├── services/
│   │   └── groqService.js     # AI integration
│   ├── styles/
│   │   └── App.css           # Mystical styling
│   ├── App.jsx               # Main game logic
│   └── main.jsx              # React entry point
├── package.json              # Dependencies
├── vite.config.js           # Build configuration
├── .env.example             # Environment template
└── README.md                # Game documentation
```

## 🎨 Features

- **AI-Powered Genie**: Uses Groq's Llama3 70B model for creative responses
- **Mystical UI**: Beautiful Arabian Nights themed interface
- **Interactive Chat**: Real-time conversation with the genie
- **Wish Counter**: Visual tracking of remaining wishes
- **Responsive Design**: Works on desktop and mobile
- **Typing Indicators**: Shows when the genie is thinking
- **Dramatic Effects**: Animations and visual flair

## 🔧 Troubleshooting

### Common Issues

1. **"API key not found" error**
   - Make sure you've created a `.env` file (not `.env.example`)
   - Verify your API key is correct and starts with `gsk_`
   - Restart the development server after adding the API key

2. **"Connection failed" error**
   - Check your internet connection
   - Verify your Groq API key is valid
   - Make sure you haven't exceeded your API rate limits

3. **App won't start**
   - Make sure Node.js is installed (`node --version`)
   - Run `npm install` to install dependencies
   - Check for any error messages in the terminal

### Development Commands

```bash
# Install dependencies
npm install

# Start development server
npm run dev

# Build for production
npm run build

# Preview production build
npm run preview

# Run linter
npm run lint
```

## 🌟 Customization

### Changing the Genie's Personality
Edit `src/services/groqService.js` and modify the `GENIE_SYSTEM_PROMPT` to change how the genie behaves.

### Styling Changes
Modify `src/styles/App.css` to change colors, fonts, or layout.

### Adding Features
- More wishes: Change the initial `wishesRemaining` value in `App.jsx`
- Different AI models: Update the model name in `groqService.js`
- New components: Add them to the `src/components/` directory

## 📝 Notes

- The game uses Vite for fast development and hot reloading
- All API calls are made client-side (not recommended for production)
- The mystical theme uses CSS gradients and animations for visual effects
- The genie's responses are generated in real-time by AI

## 🎭 Example Wishes to Try

- "I wish for a million dollars"
- "I want to be famous"
- "I wish I could fly"
- "I want to be the smartest person alive"
- "I wish for world peace"
- "I want to live forever"

Remember: The genie always finds a creative way to twist your wishes! 🧞‍♂️✨
