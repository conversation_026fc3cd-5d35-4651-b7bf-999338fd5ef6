<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>3 Wishes - AI Genie Game</title>
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Cinzel:wght@400;600;700&family=Crimson+Text:ital,wght@0,400;0,600;1,400&display=swap" rel="stylesheet">
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Crimson Text', serif;
            background: linear-gradient(135deg, #1a0033 0%, #2d1b69 25%, #8b5cf6 50%, #fbbf24 75%, #f59e0b 100%);
            background-attachment: fixed;
            min-height: 100vh;
            color: #f8fafc;
            overflow-x: hidden;
        }

        body::before {
            content: '';
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background:
                radial-gradient(circle at 20% 80%, rgba(120, 119, 198, 0.3) 0%, transparent 50%),
                radial-gradient(circle at 80% 20%, rgba(255, 215, 0, 0.2) 0%, transparent 50%),
                radial-gradient(circle at 40% 40%, rgba(139, 92, 246, 0.2) 0%, transparent 50%);
            animation: mysticalGlow 8s ease-in-out infinite alternate;
            pointer-events: none;
            z-index: -1;
        }

        @keyframes mysticalGlow {
            0% { opacity: 0.5; transform: scale(1); }
            100% { opacity: 0.8; transform: scale(1.1); }
        }

        .app {
            min-height: 100vh;
            display: flex;
            justify-content: center;
            align-items: center;
            padding: 20px;
        }

        .game-container {
            width: 100%;
            max-width: 800px;
            background: rgba(15, 23, 42, 0.9);
            border-radius: 20px;
            border: 2px solid rgba(255, 215, 0, 0.3);
            box-shadow:
                0 20px 40px rgba(0, 0, 0, 0.3),
                inset 0 1px 0 rgba(255, 255, 255, 0.1);
            backdrop-filter: blur(10px);
            overflow: hidden;
        }

        .game-header {
            text-align: center;
            padding: 30px 20px 20px;
            background: linear-gradient(135deg, rgba(139, 92, 246, 0.2) 0%, rgba(255, 215, 0, 0.1) 100%);
            border-bottom: 1px solid rgba(255, 215, 0, 0.2);
        }

        .game-title {
            font-family: 'Cinzel', serif;
            font-size: 3rem;
            font-weight: 700;
            background: linear-gradient(135deg, #fbbf24 0%, #f59e0b 50%, #d97706 100%);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
            margin-bottom: 10px;
            text-shadow: 0 0 30px rgba(251, 191, 36, 0.5);
        }

        .game-subtitle {
            font-size: 1.2rem;
            color: #cbd5e1;
            font-style: italic;
            margin-bottom: 20px;
        }

        .wish-counter {
            display: flex;
            flex-direction: column;
            align-items: center;
            gap: 10px;
        }

        .wish-lamps {
            display: flex;
            gap: 15px;
        }

        .wish-lamp {
            font-size: 2rem;
            transition: all 0.3s ease;
            filter: drop-shadow(0 0 10px rgba(255, 215, 0, 0.5));
        }

        .wish-lamp.active {
            animation: lampGlow 2s ease-in-out infinite alternate;
        }

        .wish-lamp.used {
            opacity: 0.3;
            filter: grayscale(100%);
        }

        @keyframes lampGlow {
            0% { transform: scale(1); filter: drop-shadow(0 0 10px rgba(255, 215, 0, 0.5)); }
            100% { transform: scale(1.1); filter: drop-shadow(0 0 20px rgba(255, 215, 0, 0.8)); }
        }

        .wish-status {
            font-size: 1rem;
            font-weight: 600;
            text-transform: uppercase;
            letter-spacing: 1px;
            color: #10b981;
        }

        .wish-status.final-wish {
            color: #f59e0b;
            animation: pulse 1s infinite;
        }

        .wish-status.no-wishes {
            color: #ef4444;
        }

        @keyframes pulse {
            0%, 100% { opacity: 1; }
            50% { opacity: 0.7; }
        }

        .game-main {
            padding: 20px;
            display: flex;
            flex-direction: column;
            gap: 20px;
            min-height: 500px;
        }

        .chat-container {
            flex: 1;
            background: rgba(30, 41, 59, 0.5);
            border-radius: 15px;
            border: 1px solid rgba(255, 215, 0, 0.1);
            overflow: hidden;
        }

        .messages {
            max-height: 400px;
            overflow-y: auto;
            padding: 20px;
            display: flex;
            flex-direction: column;
            gap: 15px;
        }

        .message {
            display: flex;
            flex-direction: column;
            gap: 5px;
            animation: messageSlideIn 0.3s ease-out;
        }

        @keyframes messageSlideIn {
            from { opacity: 0; transform: translateY(20px); }
            to { opacity: 1; transform: translateY(0); }
        }

        .message.user {
            align-items: flex-end;
        }

        .message.genie {
            align-items: flex-start;
        }

        .message-content {
            max-width: 80%;
            padding: 15px 20px;
            border-radius: 20px;
            line-height: 1.6;
            word-wrap: break-word;
        }

        .message.user .message-content {
            background: linear-gradient(135deg, #3b82f6 0%, #1d4ed8 100%);
            color: white;
            border-bottom-right-radius: 5px;
        }

        .message.genie .message-content {
            background: linear-gradient(135deg, rgba(139, 92, 246, 0.9) 0%, rgba(99, 102, 241, 0.9) 100%);
            color: white;
            border-bottom-left-radius: 5px;
            border: 1px solid rgba(255, 215, 0, 0.2);
        }

        .message-timestamp {
            font-size: 0.75rem;
            color: #94a3b8;
            padding: 0 10px;
        }

        .wish-input-container {
            background: rgba(30, 41, 59, 0.5);
            border-radius: 15px;
            border: 1px solid rgba(255, 215, 0, 0.1);
            padding: 20px;
        }

        .input-group {
            display: flex;
            gap: 15px;
            align-items: flex-end;
            margin-bottom: 10px;
        }

        .wish-textarea {
            flex: 1;
            background: rgba(15, 23, 42, 0.8);
            border: 2px solid rgba(255, 215, 0, 0.2);
            border-radius: 10px;
            padding: 15px;
            color: #f8fafc;
            font-family: inherit;
            font-size: 1rem;
            resize: vertical;
            min-height: 80px;
            transition: all 0.3s ease;
        }

        .wish-textarea:focus {
            outline: none;
            border-color: rgba(255, 215, 0, 0.5);
            box-shadow: 0 0 20px rgba(255, 215, 0, 0.2);
        }

        .wish-button {
            background: linear-gradient(135deg, #fbbf24 0%, #f59e0b 100%);
            color: #1f2937;
            border: none;
            border-radius: 10px;
            padding: 15px 25px;
            font-family: 'Cinzel', serif;
            font-weight: 600;
            font-size: 1rem;
            cursor: pointer;
            transition: all 0.3s ease;
            white-space: nowrap;
            box-shadow: 0 4px 15px rgba(251, 191, 36, 0.3);
        }

        .wish-button:hover:not(:disabled) {
            transform: translateY(-2px);
            box-shadow: 0 6px 20px rgba(251, 191, 36, 0.4);
        }

        .wish-button:disabled {
            opacity: 0.5;
            cursor: not-allowed;
        }

        .api-setup {
            background: rgba(239, 68, 68, 0.1);
            border: 1px solid rgba(239, 68, 68, 0.3);
            border-radius: 10px;
            padding: 20px;
            margin: 20px;
            text-align: center;
        }

        .api-input {
            width: 100%;
            max-width: 400px;
            padding: 10px;
            margin: 10px 0;
            border: 1px solid rgba(255, 215, 0, 0.3);
            border-radius: 5px;
            background: rgba(15, 23, 42, 0.8);
            color: white;
        }

        .reset-button {
            background: linear-gradient(135deg, #8b5cf6 0%, #7c3aed 100%);
            color: white;
            border: none;
            border-radius: 10px;
            padding: 15px 30px;
            font-family: 'Cinzel', serif;
            font-weight: 600;
            font-size: 1.1rem;
            cursor: pointer;
            transition: all 0.3s ease;
            box-shadow: 0 4px 15px rgba(139, 92, 246, 0.3);
            margin: 20px auto;
            display: block;
        }

        .reset-button:hover {
            transform: translateY(-2px);
            box-shadow: 0 6px 20px rgba(139, 92, 246, 0.4);
        }
    </style>
</head>
<body>
    <div class="app">
        <div class="game-container">
            <header class="game-header">
                <h1 class="game-title">🧞‍♂️ 3 Wishes</h1>
                <p class="game-subtitle">AI Genie Game</p>
                <div class="wish-counter">
                    <div class="wish-lamps" id="wishLamps">
                        <div class="wish-lamp active">🪔</div>
                        <div class="wish-lamp active">🪔</div>
                        <div class="wish-lamp active">🪔</div>
                    </div>
                    <div class="wish-status" id="wishStatus">Three wishes await you</div>
                </div>
            </header>

            <main class="game-main">
                <div id="apiSetup" class="api-setup">
                    <h3>🔑 Setup Required</h3>
                    <p>To play the game, you need a free Groq API key:</p>
                    <ol style="text-align: left; max-width: 500px; margin: 0 auto;">
                        <li>Visit <a href="https://console.groq.com/keys" target="_blank" style="color: #fbbf24;">console.groq.com/keys</a></li>
                        <li>Sign up for a free account</li>
                        <li>Create a new API key</li>
                        <li>Paste it below:</li>
                    </ol>
                    <input type="password" id="apiKeyInput" class="api-input" placeholder="Enter your Groq API key (starts with gsk_)">
                    <br>
                    <button onclick="setApiKey()" class="wish-button">🚀 Start Game</button>
                </div>

                <div class="chat-container" id="chatContainer" style="display: none;">
                    <div class="messages" id="messages"></div>
                </div>

                <div class="wish-input-container" id="wishInput" style="display: none;">
                    <div class="input-group">
                        <textarea id="wishText" class="wish-textarea" placeholder="Talk to the genie or make a wish..." rows="3" maxlength="500"></textarea>
                        <button onclick="makeWish()" class="wish-button" id="wishButton">💬 Send</button>
                    </div>
                </div>

                <button onclick="resetGame()" class="reset-button" id="resetButton" style="display: none;">🔄 Summon Genie Again</button>
            </main>
        </div>
    </div>

    <script>
        let gameState = {
            wishesRemaining: 3,
            messages: [],
            gameStarted: false,
            gameEnded: false,
            apiKey: null
        };

        function setApiKey() {
            const apiKey = document.getElementById('apiKeyInput').value.trim();
            if (!apiKey.startsWith('gsk_')) {
                alert('Please enter a valid Groq API key (starts with gsk_)');
                return;
            }

            gameState.apiKey = apiKey;
            document.getElementById('apiSetup').style.display = 'none';
            document.getElementById('chatContainer').style.display = 'block';
            document.getElementById('wishInput').style.display = 'block';

            initializeGame();
        }

        function initializeGame() {
            const introMessage = {
                type: 'genie',
                content: `🧞‍♂️ *A mystical smoke swirls before you as an ancient lamp glows...*

Greetings, mortal! I am the great AI Genie, bound by ancient magic to grant you THREE wishes. But beware... I am quite literal in my interpretations, and every wish comes with consequences you may not expect!

*The genie's eyes twinkle with mischief*

I have witnessed countless wishes over the millennia, and I must warn you - be very careful what you wish for! The universe has a sense of humor, and so do I.

What is your first desire, brave soul?`,
                timestamp: new Date().toLocaleTimeString()
            };

            gameState.messages = [introMessage];
            gameState.gameStarted = true;
            updateUI();
        }

        async function makeWish() {
            const wishText = document.getElementById('wishText').value.trim();
            if (!wishText) return;

            // Add user message
            gameState.messages.push({
                type: 'user',
                content: wishText,
                timestamp: new Date().toLocaleTimeString()
            });

            document.getElementById('wishText').value = '';
            document.getElementById('wishButton').disabled = true;
            document.getElementById('wishButton').textContent = '✨ Processing...';

            updateUI();

            try {
                const response = await processWishWithGroq(wishText);

                gameState.messages.push({
                    type: 'genie',
                    content: response,
                    timestamp: new Date().toLocaleTimeString()
                });

                // Only deduct a wish if the user actually made a wish (contains "I wish" or similar)
                const isActualWish = /\b(i wish|wish for|i want|give me|grant me)\b/i.test(wishText);
                const genieGrantedWish = /granted|poof|\*snap\*/i.test(response);

                if (isActualWish && genieGrantedWish && gameState.wishesRemaining > 0) {
                    gameState.wishesRemaining--;

                    if (gameState.wishesRemaining === 0) {
                        gameState.gameEnded = true;
                        setTimeout(() => {
                            gameState.messages.push({
                                type: 'genie',
                                content: `🧞‍♂️ *The genie begins to fade back into the lamp*

And so concludes our mystical encounter! You have used all three wishes, and I must return to my eternal slumber within the lamp.

Remember this experience well, mortal - sometimes the greatest magic is learning to appreciate what you already have!

*With a final dramatic flourish, the genie disappears in a puff of glittering smoke*

✨ Game Over ✨`,
                                timestamp: new Date().toLocaleTimeString()
                            });
                            updateUI();
                        }, 2000);
                    }
                }

            } catch (error) {
                gameState.messages.push({
                    type: 'genie',
                    content: `🧞‍♂️ *The genie looks troubled*

Alas! The mystical energies are disrupted! ${error.message}

Fear not! Your wish remains unspent. Try again when the magical energies stabilize!`,
                    timestamp: new Date().toLocaleTimeString()
                });
            }

            document.getElementById('wishButton').disabled = false;
            document.getElementById('wishButton').textContent = '💬 Send';
            updateUI();
        }

        async function processWishWithGroq(wishText) {
            // Build conversation history for memory
            const conversationMessages = [
                {
                    role: 'system',
                    content: `You are a mischievous AI Genie with conversation memory.

RULES:
1. ONLY grant wishes if the user says "I wish" or similar wish phrases
2. If they're just talking/asking questions, respond conversationally
3. Remember previous wishes and conversations
4. Keep responses SHORT (one sentence max)
5. Use emojis for flair

WISH EXAMPLES:
- "I wish for money" → "🧞‍♂️ Granted! You now have $1 million in Monopoly money!"
- "I wish to fly" → "🧞‍♂️ Granted! You can now fly... but only 2 inches off the ground!"

CONVERSATION EXAMPLES:
- "Can I rip it out?" → "🧞‍♂️ Clever mortal, but those bills are magically fused together!"
- "How does this work?" → "🧞‍♂️ Ancient genie magic, my curious friend!"

Be SHORT, WITTY, and remember the conversation!`
                }
            ];

            // Add conversation history (skip the intro message)
            gameState.messages.slice(1).forEach(msg => {
                if (msg.type === 'user') {
                    conversationMessages.push({
                        role: 'user',
                        content: msg.content
                    });
                } else if (msg.type === 'genie') {
                    conversationMessages.push({
                        role: 'assistant',
                        content: msg.content
                    });
                }
            });

            // Add current message
            conversationMessages.push({
                role: 'user',
                content: wishText
            });

            const response = await fetch('https://api.groq.com/openai/v1/chat/completions', {
                method: 'POST',
                headers: {
                    'Authorization': `Bearer ${gameState.apiKey}`,
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify({
                    model: 'llama3-70b-8192',
                    messages: conversationMessages,
                    temperature: 0.8,
                    max_tokens: 400
                })
            });

            if (!response.ok) {
                throw new Error('API request failed. Check your API key and try again.');
            }

            const data = await response.json();
            return data.choices[0]?.message?.content || "The genie seems confused by your wish...";
        }

        function updateUI() {
            // Update wish counter
            const lamps = document.querySelectorAll('.wish-lamp');
            lamps.forEach((lamp, index) => {
                if (index < gameState.wishesRemaining) {
                    lamp.className = 'wish-lamp active';
                } else {
                    lamp.className = 'wish-lamp used';
                }
            });

            // Update status
            const statusElement = document.getElementById('wishStatus');
            if (gameState.wishesRemaining === 3) {
                statusElement.textContent = 'Three wishes await you';
                statusElement.className = 'wish-status';
            } else if (gameState.wishesRemaining === 2) {
                statusElement.textContent = 'Two wishes remain';
                statusElement.className = 'wish-status';
            } else if (gameState.wishesRemaining === 1) {
                statusElement.textContent = 'One final wish';
                statusElement.className = 'wish-status final-wish';
            } else {
                statusElement.textContent = 'All wishes granted';
                statusElement.className = 'wish-status no-wishes';
            }

            // Update messages
            const messagesContainer = document.getElementById('messages');
            messagesContainer.innerHTML = '';

            gameState.messages.forEach(message => {
                const messageDiv = document.createElement('div');
                messageDiv.className = `message ${message.type}`;

                const contentDiv = document.createElement('div');
                contentDiv.className = 'message-content';
                contentDiv.innerHTML = message.content.replace(/\n/g, '<br>');

                const timestampDiv = document.createElement('div');
                timestampDiv.className = 'message-timestamp';
                timestampDiv.textContent = message.timestamp;

                messageDiv.appendChild(contentDiv);
                messageDiv.appendChild(timestampDiv);
                messagesContainer.appendChild(messageDiv);
            });

            messagesContainer.scrollTop = messagesContainer.scrollHeight;

            // Update input visibility
            if (gameState.gameEnded) {
                document.getElementById('wishInput').style.display = 'none';
                document.getElementById('resetButton').style.display = 'block';
            } else {
                document.getElementById('wishInput').style.display = 'block';
                document.getElementById('resetButton').style.display = 'none';
            }

            // Update placeholder
            const textarea = document.getElementById('wishText');
            if (gameState.wishesRemaining === 3) {
                textarea.placeholder = "Make your first wish... (e.g., 'I wish for a million dollars')";
            } else if (gameState.wishesRemaining === 2) {
                textarea.placeholder = "Your second wish awaits...";
            } else if (gameState.wishesRemaining === 1) {
                textarea.placeholder = "Choose your final wish carefully...";
            }
        }

        function resetGame() {
            gameState = {
                wishesRemaining: 3,
                messages: [],
                gameStarted: false,
                gameEnded: false,
                apiKey: gameState.apiKey
            };
            initializeGame();
        }

        // Enter key support
        document.addEventListener('DOMContentLoaded', function() {
            document.getElementById('wishText').addEventListener('keypress', function(e) {
                if (e.key === 'Enter' && !e.shiftKey) {
                    e.preventDefault();
                    makeWish();
                }
            });
        });
    </script>
</body>
</html>
