import React, { useState, useEffect } from 'react'
import GenieC<PERSON> from './components/GenieChat'
import WishInput from './components/WishInput'
import WishCounter from './components/WishCounter'
import { processWish } from './services/groqService'

function App() {
  const [gameState, setGameState] = useState({
    wishesRemaining: 3,
    messages: [],
    isProcessing: false,
    gameStarted: false,
    gameEnded: false
  })

  useEffect(() => {
    // Initialize game with genie's introduction
    if (!gameState.gameStarted) {
      const introMessage = {
        id: Date.now(),
        type: 'genie',
        content: `🧞‍♂️ *A mystical smoke swirls before you as an ancient lamp glows...*

Greetings, mortal! I am the great AI Genie, bound by ancient magic to grant you THREE wishes. But beware... I am quite literal in my interpretations, and every wish comes with consequences you may not expect!

*The genie's eyes twinkle with mischief*

I have witnessed countless wishes over the millennia, and I must warn you - be very careful what you wish for! The universe has a sense of humor, and so do I.

What is your first desire, brave soul?`,
        timestamp: new Date().toLocaleTimeString()
      }
      
      setGameState(prev => ({
        ...prev,
        messages: [introMessage],
        gameStarted: true
      }))
    }
  }, [gameState.gameStarted])

  const handleWish = async (wishText) => {
    if (gameState.wishesRemaining <= 0 || gameState.isProcessing) return

    // Add user's wish to messages
    const userMessage = {
      id: Date.now(),
      type: 'user',
      content: wishText,
      timestamp: new Date().toLocaleTimeString()
    }

    setGameState(prev => ({
      ...prev,
      messages: [...prev.messages, userMessage],
      isProcessing: true
    }))

    try {
      // Process wish with AI
      const genieResponse = await processWish(wishText, gameState.wishesRemaining)
      
      const genieMessage = {
        id: Date.now() + 1,
        type: 'genie',
        content: genieResponse,
        timestamp: new Date().toLocaleTimeString()
      }

      const newWishesRemaining = gameState.wishesRemaining - 1
      const isGameEnding = newWishesRemaining === 0

      setGameState(prev => ({
        ...prev,
        messages: [...prev.messages, genieMessage],
        wishesRemaining: newWishesRemaining,
        isProcessing: false,
        gameEnded: isGameEnding
      }))

      // Add farewell message if game is ending
      if (isGameEnding) {
        setTimeout(() => {
          const farewellMessage = {
            id: Date.now() + 2,
            type: 'genie',
            content: `🧞‍♂️ *The genie begins to fade back into the lamp*

And so concludes our mystical encounter! You have used all three wishes, and I must return to my eternal slumber within the lamp. 

Remember this experience well, mortal - sometimes the greatest magic is learning to appreciate what you already have!

*With a final dramatic flourish, the genie disappears in a puff of glittering smoke*

✨ Game Over ✨

Refresh the page if you dare to summon me again!`,
            timestamp: new Date().toLocaleTimeString()
          }
          
          setGameState(prev => ({
            ...prev,
            messages: [...prev.messages, farewellMessage]
          }))
        }, 2000)
      }

    } catch (error) {
      console.error('Error processing wish:', error)
      
      const errorMessage = {
        id: Date.now() + 1,
        type: 'genie',
        content: `🧞‍♂️ *The genie looks troubled*

Alas! The mystical energies are disrupted! It seems my connection to the cosmic forces has been interrupted. 

*whispers* (The AI service might be unavailable or you may need to check your API key)

Fear not! Your wish remains unspent. Try again when the magical energies stabilize!`,
        timestamp: new Date().toLocaleTimeString()
      }

      setGameState(prev => ({
        ...prev,
        messages: [...prev.messages, errorMessage],
        isProcessing: false
      }))
    }
  }

  const resetGame = () => {
    setGameState({
      wishesRemaining: 3,
      messages: [],
      isProcessing: false,
      gameStarted: false,
      gameEnded: false
    })
  }

  return (
    <div className="app">
      <div className="game-container">
        <header className="game-header">
          <h1 className="game-title">🧞‍♂️ 3 Wishes</h1>
          <p className="game-subtitle">AI Genie Game</p>
          <WishCounter wishesRemaining={gameState.wishesRemaining} />
        </header>

        <main className="game-main">
          <GenieChat messages={gameState.messages} isProcessing={gameState.isProcessing} />
          
          {!gameState.gameEnded && (
            <WishInput 
              onWish={handleWish} 
              disabled={gameState.isProcessing || gameState.wishesRemaining <= 0}
              wishesRemaining={gameState.wishesRemaining}
            />
          )}

          {gameState.gameEnded && (
            <div className="game-controls">
              <button onClick={resetGame} className="reset-button">
                🔄 Summon Genie Again
              </button>
            </div>
          )}
        </main>
      </div>
    </div>
  )
}

export default App
