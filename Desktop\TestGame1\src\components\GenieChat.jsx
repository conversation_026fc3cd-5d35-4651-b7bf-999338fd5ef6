import React, { useEffect, useRef } from 'react'

const GenieChat = ({ messages, isProcessing }) => {
  const messagesEndRef = useRef(null)

  const scrollToBottom = () => {
    messagesEndRef.current?.scrollIntoView({ behavior: "smooth" })
  }

  useEffect(() => {
    scrollToBottom()
  }, [messages])

  const formatMessage = (content) => {
    // Convert basic markdown-like formatting
    return content
      .replace(/\*\*(.*?)\*\*/g, '<strong>$1</strong>')
      .replace(/\*(.*?)\*/g, '<em>$1</em>')
      .split('\n').map((line, index) => (
        <span key={index}>
          {line && <span dangerouslySetInnerHTML={{ __html: line }} />}
          {index < content.split('\n').length - 1 && <br />}
        </span>
      ))
  }

  return (
    <div className="chat-container">
      <div className="messages">
        {messages.map((message) => (
          <div key={message.id} className={`message ${message.type}`}>
            <div className="message-content">
              {formatMessage(message.content)}
            </div>
            <div className="message-timestamp">
              {message.timestamp}
            </div>
          </div>
        ))}
        
        {isProcessing && (
          <div className="message genie processing">
            <div className="message-content">
              <div className="typing-indicator">
                🧞‍♂️ <em>The genie contemplates your wish...</em>
                <div className="dots">
                  <span></span>
                  <span></span>
                  <span></span>
                </div>
              </div>
            </div>
          </div>
        )}
        
        <div ref={messagesEndRef} />
      </div>
    </div>
  )
}

export default GenieChat
