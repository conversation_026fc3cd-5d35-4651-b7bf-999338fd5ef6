/* Reset and base styles */
* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}

body {
  font-family: 'Crimson Text', serif;
  background: linear-gradient(135deg, #1a0033 0%, #2d1b69 25%, #8b5cf6 50%, #fbbf24 75%, #f59e0b 100%);
  background-attachment: fixed;
  min-height: 100vh;
  color: #f8fafc;
  overflow-x: hidden;
}

/* Mystical background animation */
body::before {
  content: '';
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: 
    radial-gradient(circle at 20% 80%, rgba(120, 119, 198, 0.3) 0%, transparent 50%),
    radial-gradient(circle at 80% 20%, rgba(255, 215, 0, 0.2) 0%, transparent 50%),
    radial-gradient(circle at 40% 40%, rgba(139, 92, 246, 0.2) 0%, transparent 50%);
  animation: mysticalGlow 8s ease-in-out infinite alternate;
  pointer-events: none;
  z-index: -1;
}

@keyframes mysticalGlow {
  0% { opacity: 0.5; transform: scale(1); }
  100% { opacity: 0.8; transform: scale(1.1); }
}

/* Main app container */
.app {
  min-height: 100vh;
  display: flex;
  justify-content: center;
  align-items: center;
  padding: 20px;
}

.game-container {
  width: 100%;
  max-width: 800px;
  background: rgba(15, 23, 42, 0.9);
  border-radius: 20px;
  border: 2px solid rgba(255, 215, 0, 0.3);
  box-shadow: 
    0 20px 40px rgba(0, 0, 0, 0.3),
    inset 0 1px 0 rgba(255, 255, 255, 0.1);
  backdrop-filter: blur(10px);
  overflow: hidden;
}

/* Header styles */
.game-header {
  text-align: center;
  padding: 30px 20px 20px;
  background: linear-gradient(135deg, rgba(139, 92, 246, 0.2) 0%, rgba(255, 215, 0, 0.1) 100%);
  border-bottom: 1px solid rgba(255, 215, 0, 0.2);
}

.game-title {
  font-family: 'Cinzel', serif;
  font-size: 3rem;
  font-weight: 700;
  background: linear-gradient(135deg, #fbbf24 0%, #f59e0b 50%, #d97706 100%);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
  margin-bottom: 10px;
  text-shadow: 0 0 30px rgba(251, 191, 36, 0.5);
}

.game-subtitle {
  font-size: 1.2rem;
  color: #cbd5e1;
  font-style: italic;
  margin-bottom: 20px;
}

/* Wish counter styles */
.wish-counter {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 10px;
}

.wish-lamps {
  display: flex;
  gap: 15px;
}

.wish-lamp {
  font-size: 2rem;
  transition: all 0.3s ease;
  filter: drop-shadow(0 0 10px rgba(255, 215, 0, 0.5));
}

.wish-lamp.active {
  animation: lampGlow 2s ease-in-out infinite alternate;
}

.wish-lamp.used {
  opacity: 0.3;
  filter: grayscale(100%);
}

@keyframes lampGlow {
  0% { transform: scale(1); filter: drop-shadow(0 0 10px rgba(255, 215, 0, 0.5)); }
  100% { transform: scale(1.1); filter: drop-shadow(0 0 20px rgba(255, 215, 0, 0.8)); }
}

.wish-status {
  font-size: 1rem;
  font-weight: 600;
  text-transform: uppercase;
  letter-spacing: 1px;
}

.wish-status.wishes-available { color: #10b981; }
.wish-status.final-wish { color: #f59e0b; animation: pulse 1s infinite; }
.wish-status.no-wishes { color: #ef4444; }

@keyframes pulse {
  0%, 100% { opacity: 1; }
  50% { opacity: 0.7; }
}

/* Main game area */
.game-main {
  padding: 20px;
  display: flex;
  flex-direction: column;
  gap: 20px;
  min-height: 500px;
}

/* Chat container */
.chat-container {
  flex: 1;
  background: rgba(30, 41, 59, 0.5);
  border-radius: 15px;
  border: 1px solid rgba(255, 215, 0, 0.1);
  overflow: hidden;
}

.messages {
  max-height: 400px;
  overflow-y: auto;
  padding: 20px;
  display: flex;
  flex-direction: column;
  gap: 15px;
}

/* Custom scrollbar */
.messages::-webkit-scrollbar {
  width: 8px;
}

.messages::-webkit-scrollbar-track {
  background: rgba(15, 23, 42, 0.5);
  border-radius: 4px;
}

.messages::-webkit-scrollbar-thumb {
  background: rgba(255, 215, 0, 0.3);
  border-radius: 4px;
}

.messages::-webkit-scrollbar-thumb:hover {
  background: rgba(255, 215, 0, 0.5);
}

/* Message styles */
.message {
  display: flex;
  flex-direction: column;
  gap: 5px;
  animation: messageSlideIn 0.3s ease-out;
}

@keyframes messageSlideIn {
  from { opacity: 0; transform: translateY(20px); }
  to { opacity: 1; transform: translateY(0); }
}

.message.user {
  align-items: flex-end;
}

.message.genie {
  align-items: flex-start;
}

.message-content {
  max-width: 80%;
  padding: 15px 20px;
  border-radius: 20px;
  line-height: 1.6;
  word-wrap: break-word;
}

.message.user .message-content {
  background: linear-gradient(135deg, #3b82f6 0%, #1d4ed8 100%);
  color: white;
  border-bottom-right-radius: 5px;
}

.message.genie .message-content {
  background: linear-gradient(135deg, rgba(139, 92, 246, 0.9) 0%, rgba(99, 102, 241, 0.9) 100%);
  color: white;
  border-bottom-left-radius: 5px;
  border: 1px solid rgba(255, 215, 0, 0.2);
}

.message-timestamp {
  font-size: 0.75rem;
  color: #94a3b8;
  padding: 0 10px;
}

/* Processing message */
.message.processing .message-content {
  background: rgba(139, 92, 246, 0.3);
  border: 1px dashed rgba(255, 215, 0, 0.5);
}

.typing-indicator {
  display: flex;
  align-items: center;
  gap: 10px;
}

.dots {
  display: flex;
  gap: 4px;
}

.dots span {
  width: 8px;
  height: 8px;
  background: #fbbf24;
  border-radius: 50%;
  animation: typingDots 1.4s infinite ease-in-out;
}

.dots span:nth-child(1) { animation-delay: -0.32s; }
.dots span:nth-child(2) { animation-delay: -0.16s; }

@keyframes typingDots {
  0%, 80%, 100% { transform: scale(0); opacity: 0.5; }
  40% { transform: scale(1); opacity: 1; }
}

/* Wish input styles */
.wish-input-container {
  background: rgba(30, 41, 59, 0.5);
  border-radius: 15px;
  border: 1px solid rgba(255, 215, 0, 0.1);
  padding: 20px;
}

.wish-form {
  display: flex;
  flex-direction: column;
  gap: 10px;
}

.input-group {
  display: flex;
  gap: 15px;
  align-items: flex-end;
}

.wish-textarea {
  flex: 1;
  background: rgba(15, 23, 42, 0.8);
  border: 2px solid rgba(255, 215, 0, 0.2);
  border-radius: 10px;
  padding: 15px;
  color: #f8fafc;
  font-family: inherit;
  font-size: 1rem;
  resize: vertical;
  min-height: 80px;
  transition: all 0.3s ease;
}

.wish-textarea:focus {
  outline: none;
  border-color: rgba(255, 215, 0, 0.5);
  box-shadow: 0 0 20px rgba(255, 215, 0, 0.2);
}

.wish-textarea::placeholder {
  color: #94a3b8;
  font-style: italic;
}

.wish-button {
  background: linear-gradient(135deg, #fbbf24 0%, #f59e0b 100%);
  color: #1f2937;
  border: none;
  border-radius: 10px;
  padding: 15px 25px;
  font-family: 'Cinzel', serif;
  font-weight: 600;
  font-size: 1rem;
  cursor: pointer;
  transition: all 0.3s ease;
  white-space: nowrap;
  box-shadow: 0 4px 15px rgba(251, 191, 36, 0.3);
}

.wish-button:hover:not(:disabled) {
  transform: translateY(-2px);
  box-shadow: 0 6px 20px rgba(251, 191, 36, 0.4);
}

.wish-button:disabled {
  opacity: 0.5;
  cursor: not-allowed;
  transform: none;
}

.input-footer {
  display: flex;
  justify-content: space-between;
  align-items: center;
  font-size: 0.8rem;
  color: #94a3b8;
}

.character-count {
  color: #64748b;
}

.hint {
  font-style: italic;
}

/* Game controls */
.game-controls {
  text-align: center;
  padding: 20px;
}

.reset-button {
  background: linear-gradient(135deg, #8b5cf6 0%, #7c3aed 100%);
  color: white;
  border: none;
  border-radius: 10px;
  padding: 15px 30px;
  font-family: 'Cinzel', serif;
  font-weight: 600;
  font-size: 1.1rem;
  cursor: pointer;
  transition: all 0.3s ease;
  box-shadow: 0 4px 15px rgba(139, 92, 246, 0.3);
}

.reset-button:hover {
  transform: translateY(-2px);
  box-shadow: 0 6px 20px rgba(139, 92, 246, 0.4);
}

/* Responsive design */
@media (max-width: 768px) {
  .game-title {
    font-size: 2rem;
  }
  
  .input-group {
    flex-direction: column;
    align-items: stretch;
  }
  
  .wish-button {
    align-self: center;
    min-width: 200px;
  }
  
  .input-footer {
    flex-direction: column;
    gap: 5px;
    text-align: center;
  }
  
  .message-content {
    max-width: 95%;
  }
}
