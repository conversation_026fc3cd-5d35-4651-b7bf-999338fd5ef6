<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>3 Wishes - AI Genie Game</title>
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Cinzel:wght@400;600;700&family=Crimson+Text:ital,wght@0,400;0,600;1,400&display=swap" rel="stylesheet">
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Crimson Text', serif;
            min-height: 100vh;
            color: #f8fafc;
            overflow-x: hidden;
            transition: all 0.8s ease;
        }

        /* Dynamic backgrounds based on screen */
        body.setup-bg {
            background: linear-gradient(135deg, #1a0033 0%, #2d1b69 25%, #4c1d95 50%, #7c3aed 75%, #8b5cf6 100%);
            background-attachment: fixed;
        }

        body.menu-bg {
            background: linear-gradient(135deg, #0f172a 0%, #1e293b 25%, #334155 50%, #475569 75%, #64748b 100%);
            background-attachment: fixed;
        }

        body.game-bg {
            background: linear-gradient(135deg, #1a0033 0%, #2d1b69 25%, #8b5cf6 50%, #fbbf24 75%, #f59e0b 100%);
            background-attachment: fixed;
        }

        /* Mystical background animation */
        body::before {
            content: '';
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            pointer-events: none;
            z-index: -1;
            transition: all 0.8s ease;
        }

        body.setup-bg::before {
            background:
                radial-gradient(circle at 30% 70%, rgba(124, 58, 237, 0.4) 0%, transparent 50%),
                radial-gradient(circle at 70% 30%, rgba(139, 92, 246, 0.3) 0%, transparent 50%),
                radial-gradient(circle at 50% 50%, rgba(76, 29, 149, 0.2) 0%, transparent 50%);
            animation: setupGlow 10s ease-in-out infinite alternate;
        }

        body.menu-bg::before {
            background:
                radial-gradient(circle at 20% 80%, rgba(71, 85, 105, 0.3) 0%, transparent 50%),
                radial-gradient(circle at 80% 20%, rgba(100, 116, 139, 0.2) 0%, transparent 50%),
                radial-gradient(circle at 40% 40%, rgba(51, 65, 85, 0.3) 0%, transparent 50%);
            animation: menuGlow 12s ease-in-out infinite alternate;
        }

        body.game-bg::before {
            background:
                radial-gradient(circle at 20% 80%, rgba(120, 119, 198, 0.3) 0%, transparent 50%),
                radial-gradient(circle at 80% 20%, rgba(255, 215, 0, 0.2) 0%, transparent 50%),
                radial-gradient(circle at 40% 40%, rgba(139, 92, 246, 0.2) 0%, transparent 50%);
            animation: gameGlow 8s ease-in-out infinite alternate;
        }

        @keyframes setupGlow {
            0% { opacity: 0.4; transform: scale(1) rotate(0deg); }
            100% { opacity: 0.7; transform: scale(1.1) rotate(5deg); }
        }

        @keyframes menuGlow {
            0% { opacity: 0.3; transform: scale(1) rotate(0deg); }
            100% { opacity: 0.6; transform: scale(1.05) rotate(-3deg); }
        }

        @keyframes gameGlow {
            0% { opacity: 0.5; transform: scale(1); }
            100% { opacity: 0.8; transform: scale(1.1); }
        }

        .app {
            min-height: 100vh;
            display: flex;
            justify-content: center;
            align-items: center;
            padding: 20px;
        }

        .game-container {
            width: 100%;
            max-width: 800px;
            background: rgba(15, 23, 42, 0.9);
            border-radius: 20px;
            border: 2px solid rgba(255, 215, 0, 0.3);
            box-shadow:
                0 20px 40px rgba(0, 0, 0, 0.3),
                inset 0 1px 0 rgba(255, 255, 255, 0.1);
            backdrop-filter: blur(10px);
            overflow: hidden;
        }

        .game-header {
            text-align: center;
            padding: 30px 20px 20px;
            background: linear-gradient(135deg, rgba(139, 92, 246, 0.2) 0%, rgba(255, 215, 0, 0.1) 100%);
            border-bottom: 1px solid rgba(255, 215, 0, 0.2);
        }

        .game-title {
            font-family: 'Cinzel', serif;
            font-size: 3rem;
            font-weight: 700;
            background: linear-gradient(135deg, #fbbf24 0%, #f59e0b 50%, #d97706 100%);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
            margin-bottom: 10px;
            text-shadow: 0 0 30px rgba(251, 191, 36, 0.5);
        }

        .game-subtitle {
            font-size: 1.2rem;
            color: #cbd5e1;
            font-style: italic;
            margin-bottom: 20px;
        }

        .wish-counter {
            display: flex;
            flex-direction: column;
            align-items: center;
            gap: 10px;
        }

        .wish-lamps {
            display: flex;
            gap: 15px;
        }

        .wish-lamp {
            font-size: 2rem;
            transition: all 0.3s ease;
            filter: drop-shadow(0 0 10px rgba(255, 215, 0, 0.5));
        }

        .wish-lamp.active {
            animation: lampGlow 2s ease-in-out infinite alternate;
        }

        .wish-lamp.used {
            opacity: 0.3;
            filter: grayscale(100%);
        }

        @keyframes lampGlow {
            0% { transform: scale(1); filter: drop-shadow(0 0 10px rgba(255, 215, 0, 0.5)); }
            100% { transform: scale(1.1); filter: drop-shadow(0 0 20px rgba(255, 215, 0, 0.8)); }
        }

        .wish-status {
            font-size: 1rem;
            font-weight: 600;
            text-transform: uppercase;
            letter-spacing: 1px;
            color: #10b981;
        }

        .wish-status.final-wish {
            color: #f59e0b;
            animation: pulse 1s infinite;
        }

        .wish-status.no-wishes {
            color: #ef4444;
        }

        @keyframes pulse {
            0%, 100% { opacity: 1; }
            50% { opacity: 0.7; }
        }

        .game-main {
            padding: 20px;
            display: flex;
            flex-direction: column;
            gap: 20px;
            min-height: 500px;
        }

        .chat-container {
            flex: 1;
            background: rgba(30, 41, 59, 0.5);
            border-radius: 15px;
            border: 1px solid rgba(255, 215, 0, 0.1);
            overflow: hidden;
        }

        .messages {
            max-height: 400px;
            overflow-y: auto;
            padding: 20px;
            display: flex;
            flex-direction: column;
            gap: 15px;
        }

        .message {
            display: flex;
            flex-direction: column;
            gap: 5px;
            animation: messageSlideIn 0.3s ease-out;
        }

        @keyframes messageSlideIn {
            from { opacity: 0; transform: translateY(20px); }
            to { opacity: 1; transform: translateY(0); }
        }

        .message.user {
            align-items: flex-end;
        }

        .message.genie {
            align-items: flex-start;
        }

        .message-content {
            max-width: 80%;
            padding: 15px 20px;
            border-radius: 20px;
            line-height: 1.6;
            word-wrap: break-word;
        }

        .message.user .message-content {
            background: linear-gradient(135deg, #3b82f6 0%, #1d4ed8 100%);
            color: white;
            border-bottom-right-radius: 5px;
        }

        .message.genie .message-content {
            background: linear-gradient(135deg, rgba(139, 92, 246, 0.9) 0%, rgba(99, 102, 241, 0.9) 100%);
            color: white;
            border-bottom-left-radius: 5px;
            border: 1px solid rgba(255, 215, 0, 0.2);
        }

        .message-timestamp {
            font-size: 0.75rem;
            color: #94a3b8;
            padding: 0 10px;
        }

        .wish-input-container {
            background: rgba(30, 41, 59, 0.5);
            border-radius: 15px;
            border: 1px solid rgba(255, 215, 0, 0.1);
            padding: 20px;
        }

        .input-group {
            display: flex;
            gap: 15px;
            align-items: flex-end;
            margin-bottom: 10px;
        }

        .wish-textarea {
            flex: 1;
            background: rgba(15, 23, 42, 0.8);
            border: 2px solid rgba(255, 215, 0, 0.2);
            border-radius: 10px;
            padding: 15px;
            color: #f8fafc;
            font-family: inherit;
            font-size: 1rem;
            resize: vertical;
            min-height: 80px;
            transition: all 0.3s ease;
        }

        .wish-textarea:focus {
            outline: none;
            border-color: rgba(255, 215, 0, 0.5);
            box-shadow: 0 0 20px rgba(255, 215, 0, 0.2);
        }

        .wish-button {
            background: linear-gradient(135deg, #fbbf24 0%, #f59e0b 100%);
            color: #1f2937;
            border: none;
            border-radius: 10px;
            padding: 15px 25px;
            font-family: 'Cinzel', serif;
            font-weight: 600;
            font-size: 1rem;
            cursor: pointer;
            transition: all 0.3s ease;
            white-space: nowrap;
            box-shadow: 0 4px 15px rgba(251, 191, 36, 0.3);
        }

        .wish-button:hover:not(:disabled) {
            transform: translateY(-2px);
            box-shadow: 0 6px 20px rgba(251, 191, 36, 0.4);
        }

        .wish-button:disabled {
            opacity: 0.5;
            cursor: not-allowed;
        }

        .api-setup {
            background: rgba(239, 68, 68, 0.1);
            border: 1px solid rgba(239, 68, 68, 0.3);
            border-radius: 10px;
            padding: 20px;
            margin: 20px;
            text-align: center;
        }

        .api-input {
            width: 100%;
            max-width: 400px;
            padding: 10px;
            margin: 10px 0;
            border: 1px solid rgba(255, 215, 0, 0.3);
            border-radius: 5px;
            background: rgba(15, 23, 42, 0.8);
            color: white;
        }

        .reset-button {
            background: linear-gradient(135deg, #8b5cf6 0%, #7c3aed 100%);
            color: white;
            border: none;
            border-radius: 10px;
            padding: 15px 30px;
            font-family: 'Cinzel', serif;
            font-weight: 600;
            font-size: 1.1rem;
            cursor: pointer;
            transition: all 0.3s ease;
            box-shadow: 0 4px 15px rgba(139, 92, 246, 0.3);
            margin: 20px auto;
            display: block;
        }

        .reset-button:hover {
            transform: translateY(-2px);
            box-shadow: 0 6px 20px rgba(139, 92, 246, 0.4);
        }

        /* Screen management */
        .screen {
            min-height: 100vh;
            display: flex;
            justify-content: center;
            align-items: center;
            padding: 20px;
        }

        /* Setup screen styles */
        .setup-container {
            width: 100%;
            max-width: 600px;
            background: rgba(15, 23, 42, 0.9);
            border-radius: 20px;
            border: 2px solid rgba(255, 215, 0, 0.3);
            box-shadow:
                0 20px 40px rgba(0, 0, 0, 0.3),
                inset 0 1px 0 rgba(255, 255, 255, 0.1);
            backdrop-filter: blur(10px);
            padding: 40px;
            text-align: center;
        }

        .main-title {
            font-family: 'Cinzel', serif;
            font-size: 3rem;
            font-weight: 700;
            background: linear-gradient(135deg, #fbbf24 0%, #f59e0b 50%, #d97706 100%);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
            margin-bottom: 10px;
            text-shadow: 0 0 30px rgba(251, 191, 36, 0.5);
        }

        .main-subtitle {
            font-size: 1.2rem;
            color: #cbd5e1;
            font-style: italic;
            margin-bottom: 30px;
        }

        .setup-content h3 {
            color: #fbbf24;
            margin-bottom: 20px;
            font-size: 1.5rem;
        }

        .setup-steps {
            text-align: left;
            max-width: 500px;
            margin: 20px auto;
            color: #e2e8f0;
            line-height: 1.6;
        }

        .setup-steps li {
            margin-bottom: 10px;
        }

        .api-input-group {
            margin: 30px 0;
        }

        .setup-button {
            background: linear-gradient(135deg, #10b981 0%, #059669 100%);
            color: white;
            border: none;
            border-radius: 10px;
            padding: 15px 30px;
            font-family: 'Cinzel', serif;
            font-weight: 600;
            font-size: 1rem;
            cursor: pointer;
            transition: all 0.3s ease;
            margin-left: 10px;
            box-shadow: 0 4px 15px rgba(16, 185, 129, 0.3);
        }

        .setup-button:hover:not(:disabled) {
            transform: translateY(-2px);
            box-shadow: 0 6px 20px rgba(16, 185, 129, 0.4);
        }

        .setup-button:disabled {
            opacity: 0.5;
            cursor: not-allowed;
        }

        .setup-status {
            margin-top: 20px;
            padding: 15px;
            border-radius: 10px;
            font-weight: 600;
        }

        .setup-status.success {
            background: rgba(16, 185, 129, 0.2);
            border: 1px solid rgba(16, 185, 129, 0.3);
            color: #10b981;
        }

        .setup-status.error {
            background: rgba(239, 68, 68, 0.2);
            border: 1px solid rgba(239, 68, 68, 0.3);
            color: #ef4444;
        }

        .setup-status.testing {
            background: rgba(251, 191, 36, 0.2);
            border: 1px solid rgba(251, 191, 36, 0.3);
            color: #fbbf24;
        }

        /* Main menu styles */
        .menu-container {
            width: 100%;
            max-width: 900px;
            background: rgba(15, 23, 42, 0.9);
            border-radius: 20px;
            border: 2px solid rgba(255, 215, 0, 0.3);
            box-shadow:
                0 20px 40px rgba(0, 0, 0, 0.3),
                inset 0 1px 0 rgba(255, 255, 255, 0.1);
            backdrop-filter: blur(10px);
            padding: 40px;
            text-align: center;
        }

        .games-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 20px;
            margin: 40px 0;
        }

        .game-card {
            background: rgba(30, 41, 59, 0.8);
            border-radius: 15px;
            border: 2px solid rgba(255, 215, 0, 0.2);
            padding: 30px 20px;
            cursor: pointer;
            transition: all 0.3s ease;
            text-align: center;
        }

        .game-card:hover:not(.coming-soon) {
            transform: translateY(-5px);
            border-color: rgba(255, 215, 0, 0.5);
            box-shadow: 0 10px 30px rgba(255, 215, 0, 0.2);
        }

        .game-card.coming-soon {
            opacity: 0.6;
            cursor: not-allowed;
        }

        .game-icon {
            font-size: 3rem;
            margin-bottom: 15px;
        }

        .game-card h3 {
            color: #fbbf24;
            margin-bottom: 15px;
            font-size: 1.3rem;
        }

        .game-card p {
            color: #cbd5e1;
            line-height: 1.5;
            margin-bottom: 15px;
        }

        .game-status {
            font-weight: 600;
            padding: 8px 15px;
            border-radius: 20px;
            font-size: 0.9rem;
        }

        .game-card:not(.coming-soon) .game-status {
            background: rgba(16, 185, 129, 0.2);
            color: #10b981;
        }

        .game-card.coming-soon .game-status {
            background: rgba(107, 114, 128, 0.2);
            color: #9ca3af;
        }

        .menu-footer {
            margin-top: 30px;
            padding-top: 20px;
            border-top: 1px solid rgba(255, 215, 0, 0.2);
        }

        .settings-button {
            background: rgba(107, 114, 128, 0.3);
            color: #cbd5e1;
            border: 1px solid rgba(107, 114, 128, 0.5);
            border-radius: 10px;
            padding: 10px 20px;
            font-size: 0.9rem;
            cursor: pointer;
            transition: all 0.3s ease;
        }

        .settings-button:hover {
            background: rgba(107, 114, 128, 0.5);
            transform: translateY(-1px);
        }

        /* Back button */
        .back-button {
            position: absolute;
            top: 20px;
            left: 20px;
            background: rgba(107, 114, 128, 0.3);
            color: #cbd5e1;
            border: 1px solid rgba(107, 114, 128, 0.5);
            border-radius: 8px;
            padding: 8px 15px;
            font-size: 0.9rem;
            cursor: pointer;
            transition: all 0.3s ease;
        }

        .back-button:hover {
            background: rgba(107, 114, 128, 0.5);
            transform: translateY(-1px);
        }

        /* Responsive adjustments */
        @media (max-width: 768px) {
            .main-title {
                font-size: 2rem;
            }

            .games-grid {
                grid-template-columns: 1fr;
            }

            .setup-container, .menu-container {
                padding: 20px;
            }
        }
    </style>
</head>
<body>
    <div class="app">
        <!-- Setup Screen -->
        <div id="setupScreen" class="screen">
            <div class="setup-container">
                <h1 class="main-title">🧞‍♂️ AI Genie Games</h1>
                <p class="main-subtitle">Magical AI-Powered Adventures</p>

                <div class="setup-content">
                    <h3>🔑 First Time Setup</h3>
                    <p>To play these games, you need a free Groq API key:</p>
                    <ol class="setup-steps">
                        <li>Visit <a href="https://console.groq.com/keys" target="_blank" style="color: #fbbf24;">console.groq.com/keys</a></li>
                        <li>Sign up for a free account (completely free!)</li>
                        <li>Create a new API key</li>
                        <li>Paste it below:</li>
                    </ol>
                    <div class="api-input-group">
                        <input type="password" id="apiKeyInput" class="api-input" placeholder="Enter your Groq API key (starts with gsk_)">
                        <button onclick="setupApiKey()" class="setup-button" id="setupButton">🔧 Setup & Test</button>
                    </div>
                    <div id="setupStatus" class="setup-status"></div>
                </div>
            </div>
        </div>

        <!-- Main Menu -->
        <div id="mainMenu" class="screen" style="display: none;">
            <div class="menu-container">
                <h1 class="main-title">🧞‍♂️ AI Genie Games</h1>
                <p class="main-subtitle">Choose Your Magical Adventure</p>

                <div class="games-grid">
                    <div class="game-card" onclick="startGame('unwishing')">
                        <div class="game-icon">🧞‍♂️</div>
                        <h3>Unwishing Genie</h3>
                        <p>Make 3 wishes, but beware - every wish comes with a clever twist!</p>
                        <div class="game-status">✨ Available</div>
                    </div>

                    <div class="game-card coming-soon">
                        <div class="game-icon">🔮</div>
                        <h3>Crystal Ball Oracle</h3>
                        <p>Ask the mystical oracle about your future...</p>
                        <div class="game-status">🚧 Coming Soon</div>
                    </div>

                    <div class="game-card coming-soon">
                        <div class="game-icon">🐉</div>
                        <h3>Dragon's Riddle</h3>
                        <p>Solve the ancient dragon's riddles to claim treasure...</p>
                        <div class="game-status">🚧 Coming Soon</div>
                    </div>
                </div>

                <div class="menu-footer">
                    <button onclick="showSetup()" class="settings-button">⚙️ Change API Key</button>
                </div>
            </div>
        </div>

        <!-- Game Screen -->
        <div id="gameScreen" class="screen" style="display: none;">
            <div class="game-container">
                <header class="game-header">
                    <button onclick="backToMenu()" class="back-button">← Back to Menu</button>
                    <h1 class="game-title" id="gameTitle">🧞‍♂️ Unwishing Genie</h1>
                    <p class="game-subtitle" id="gameSubtitle">AI Genie Game</p>
                    <div class="wish-counter" id="wishCounter">
                        <div class="wish-lamps" id="wishLamps">
                            <div class="wish-lamp active">🪔</div>
                            <div class="wish-lamp active">🪔</div>
                            <div class="wish-lamp active">🪔</div>
                        </div>
                        <div class="wish-status" id="wishStatus">Three wishes await you</div>
                    </div>
                </header>

                <main class="game-main">
                    <div class="chat-container" id="chatContainer">
                        <div class="messages" id="messages"></div>
                    </div>

                    <div class="wish-input-container" id="wishInput">
                        <div class="input-group">
                            <textarea id="wishText" class="wish-textarea" placeholder="Talk to the genie or make a wish..." rows="3" maxlength="500"></textarea>
                            <button onclick="makeWish()" class="wish-button" id="wishButton">💬 Send</button>
                        </div>
                    </div>

                    <button onclick="resetCurrentGame()" class="reset-button" id="resetButton" style="display: none;">🔄 Play Again</button>
                </main>
            </div>
        </div>
    </div>

    <script>
        let appState = {
            apiKey: null,
            currentScreen: 'setup',
            currentGame: null
        };

        let gameState = {
            wishesRemaining: 3,
            messages: [],
            gameStarted: false,
            gameEnded: false
        };

        // Screen management
        function showScreen(screenName) {
            // Hide all screens
            document.querySelectorAll('.screen').forEach(screen => {
                screen.style.display = 'none';
            });

            // Show target screen
            const targetScreen = document.getElementById(screenName + 'Screen');
            if (targetScreen) {
                targetScreen.style.display = 'flex';
            }

            // Update background class
            document.body.className = '';
            document.body.classList.add(screenName + '-bg');

            appState.currentScreen = screenName;
            console.log('Switched to screen:', screenName);
        }

        function showSetup() {
            showScreen('setup');
            document.getElementById('apiKeyInput').value = appState.apiKey || '';
        }

        function showMainMenu() {
            showScreen('mainMenu');
        }

        function showGame() {
            showScreen('game');
        }

        // API setup and testing
        async function setupApiKey() {
            const apiKey = document.getElementById('apiKeyInput').value.trim();
            const statusDiv = document.getElementById('setupStatus');
            const button = document.getElementById('setupButton');

            if (!apiKey) {
                statusDiv.className = 'setup-status error';
                statusDiv.textContent = '❌ Please enter an API key';
                return;
            }

            if (!apiKey.startsWith('gsk_')) {
                statusDiv.className = 'setup-status error';
                statusDiv.textContent = '❌ Invalid API key format (should start with gsk_)';
                return;
            }

            // Test the API key
            button.disabled = true;
            button.textContent = '🔄 Testing...';
            statusDiv.className = 'setup-status testing';
            statusDiv.textContent = '🧪 Testing API connection...';

            try {
                await testApiConnection(apiKey);

                // Success!
                appState.apiKey = apiKey;
                statusDiv.className = 'setup-status success';
                statusDiv.textContent = '✅ API key verified! Redirecting to main menu...';

                // Save API key
                saveApiKey(apiKey);

                setTimeout(() => {
                    showMainMenu();
                }, 1500);

            } catch (error) {
                statusDiv.className = 'setup-status error';
                statusDiv.textContent = `❌ API test failed: ${error.message}`;
                button.disabled = false;
                button.textContent = '🔧 Setup & Test';
            }
        }

        async function testApiConnection(apiKey) {
            const response = await fetch('https://api.groq.com/openai/v1/chat/completions', {
                method: 'POST',
                headers: {
                    'Authorization': `Bearer ${apiKey}`,
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify({
                    model: 'llama3-70b-8192',
                    messages: [
                        {
                            role: 'user',
                            content: 'Say "API test successful" if you can hear me.'
                        }
                    ],
                    max_tokens: 10
                })
            });

            if (!response.ok) {
                if (response.status === 401) {
                    throw new Error('Invalid API key');
                } else if (response.status === 429) {
                    throw new Error('Rate limit exceeded');
                } else {
                    throw new Error(`API error: ${response.status}`);
                }
            }

            const data = await response.json();
            const content = data.choices[0]?.message?.content || '';

            if (!content.toLowerCase().includes('successful')) {
                throw new Error('Unexpected API response');
            }
        }

        // Game management
        function startGame(gameType) {
            appState.currentGame = gameType;

            if (gameType === 'unwishing') {
                document.getElementById('gameTitle').textContent = '🧞‍♂️ Unwishing Genie';
                document.getElementById('gameSubtitle').textContent = 'AI Genie Game';
                document.getElementById('wishCounter').style.display = 'flex';
                initializeUnwishingGenie();
            }

            showGame();
        }

        function backToMenu() {
            showMainMenu();
        }

        function resetCurrentGame() {
            if (appState.currentGame === 'unwishing') {
                initializeUnwishingGenie();
            }
        }

        // Unwishing Genie Game
        function initializeUnwishingGenie() {
            gameState = {
                wishesRemaining: 3,
                messages: [],
                gameStarted: false,
                gameEnded: false
            };

            initializeGame();
        }

        function initializeGame() {
            const introMessage = {
                type: 'genie',
                content: `🧞‍♂️ *A mystical smoke swirls before you as an ancient lamp glows...*

Greetings, mortal! I am the great AI Genie, bound by ancient magic to grant you THREE wishes. But beware... I am quite literal in my interpretations, and every wish comes with consequences you may not expect!

*The genie's eyes twinkle with mischief*

I have witnessed countless wishes over the millennia, and I must warn you - be very careful what you wish for! The universe has a sense of humor, and so do I.

What is your first desire, brave soul?`,
                timestamp: new Date().toLocaleTimeString()
            };

            gameState.messages = [introMessage];
            gameState.gameStarted = true;
            updateUI();
        }

        async function makeWish() {
            const wishText = document.getElementById('wishText').value.trim();
            if (!wishText) return;

            // Add user message
            gameState.messages.push({
                type: 'user',
                content: wishText,
                timestamp: new Date().toLocaleTimeString()
            });

            document.getElementById('wishText').value = '';
            document.getElementById('wishButton').disabled = true;
            document.getElementById('wishButton').textContent = '✨ Processing...';

            updateUI();

            try {
                const response = await processWishWithGroq(wishText);

                gameState.messages.push({
                    type: 'genie',
                    content: response,
                    timestamp: new Date().toLocaleTimeString()
                });

                // Only deduct a wish if the user actually made a wish (contains "I wish" or similar)
                const isActualWish = /\b(i wish|wish for|i want|give me|grant me|make me|let me)\b/i.test(wishText);
                const genieGrantedWish = /granted|poof|\*snap\*|✨|🧞‍♂️.*granted/i.test(response);

                // Fallback: if user says "I wish" specifically and genie responds, count it
                const saysIWish = /\bi wish\b/i.test(wishText);
                const genieResponded = /🧞‍♂️/i.test(response);

                console.log('Wish detection:', {
                    wishText,
                    isActualWish,
                    saysIWish,
                    response: response.substring(0, 50) + '...',
                    genieGrantedWish,
                    genieResponded,
                    wishesRemaining: gameState.wishesRemaining
                });

                if (gameState.wishesRemaining > 0 && ((isActualWish && genieGrantedWish) || (saysIWish && genieResponded))) {
                    gameState.wishesRemaining--;
                    console.log('Wish deducted! Remaining:', gameState.wishesRemaining);

                    if (gameState.wishesRemaining === 0) {
                        gameState.gameEnded = true;
                        setTimeout(() => {
                            gameState.messages.push({
                                type: 'genie',
                                content: `🧞‍♂️ *The genie begins to fade back into the lamp*

And so concludes our mystical encounter! You have used all three wishes, and I must return to my eternal slumber within the lamp.

Remember this experience well, mortal - sometimes the greatest magic is learning to appreciate what you already have!

*With a final dramatic flourish, the genie disappears in a puff of glittering smoke*

✨ Game Over ✨`,
                                timestamp: new Date().toLocaleTimeString()
                            });
                            updateUI();
                        }, 2000);
                    }
                }

            } catch (error) {
                gameState.messages.push({
                    type: 'genie',
                    content: `🧞‍♂️ *The genie looks troubled*

Alas! The mystical energies are disrupted! ${error.message}

Fear not! Your wish remains unspent. Try again when the magical energies stabilize!`,
                    timestamp: new Date().toLocaleTimeString()
                });
            }

            document.getElementById('wishButton').disabled = false;
            document.getElementById('wishButton').textContent = '💬 Send';
            updateUI();
        }

        async function processWishWithGroq(wishText) {
            // Build conversation history for memory
            const conversationMessages = [
                {
                    role: 'system',
                    content: `You are a mischievous AI Genie with conversation memory.

RULES:
1. ONLY grant wishes if the user says "I wish" or similar wish phrases
2. If they're just talking/asking questions, respond conversationally and DIRECTLY
3. Remember previous wishes and conversations
4. Keep responses SHORT (one sentence max)
5. Use emojis for flair
6. Be direct and honest, not evasive or riddling
7. Answer questions clearly and simply

WISH EXAMPLES:
- "I wish for money" → "🧞‍♂️ Granted! You now have $1 million in Monopoly money!"
- "I wish to fly" → "🧞‍♂️ Granted! You can now fly... but only 2 inches off the ground!"

CONVERSATION EXAMPLES:
- "Can I rip it out?" → "🧞‍♂️ Clever mortal, but those bills are magically fused together!"
- "How does this work?" → "🧞‍♂️ Ancient genie magic, my curious friend!"

Be SHORT, WITTY, DIRECT, and remember the conversation!`
                }
            ];

            // Add conversation history (skip the intro message)
            gameState.messages.slice(1).forEach(msg => {
                if (msg.type === 'user') {
                    conversationMessages.push({
                        role: 'user',
                        content: msg.content
                    });
                } else if (msg.type === 'genie') {
                    conversationMessages.push({
                        role: 'assistant',
                        content: msg.content
                    });
                }
            });

            // Add current message
            conversationMessages.push({
                role: 'user',
                content: wishText
            });

            const response = await fetch('https://api.groq.com/openai/v1/chat/completions', {
                method: 'POST',
                headers: {
                    'Authorization': `Bearer ${appState.apiKey}`,
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify({
                    model: 'llama3-70b-8192',
                    messages: conversationMessages,
                    temperature: 0.8,
                    max_tokens: 400
                })
            });

            if (!response.ok) {
                throw new Error('API request failed. Check your API key and try again.');
            }

            const data = await response.json();
            return data.choices[0]?.message?.content || "The genie seems confused by your wish...";
        }

        function updateUI() {
            // Update wish counter
            const lamps = document.querySelectorAll('.wish-lamp');
            lamps.forEach((lamp, index) => {
                if (index < gameState.wishesRemaining) {
                    lamp.className = 'wish-lamp active';
                } else {
                    lamp.className = 'wish-lamp used';
                }
            });

            // Update status
            const statusElement = document.getElementById('wishStatus');
            if (gameState.wishesRemaining === 3) {
                statusElement.textContent = 'Three wishes await you';
                statusElement.className = 'wish-status';
            } else if (gameState.wishesRemaining === 2) {
                statusElement.textContent = 'Two wishes remain';
                statusElement.className = 'wish-status';
            } else if (gameState.wishesRemaining === 1) {
                statusElement.textContent = 'One final wish';
                statusElement.className = 'wish-status final-wish';
            } else {
                statusElement.textContent = 'All wishes granted';
                statusElement.className = 'wish-status no-wishes';
            }

            // Update messages
            const messagesContainer = document.getElementById('messages');
            messagesContainer.innerHTML = '';

            gameState.messages.forEach(message => {
                const messageDiv = document.createElement('div');
                messageDiv.className = `message ${message.type}`;

                const contentDiv = document.createElement('div');
                contentDiv.className = 'message-content';
                contentDiv.innerHTML = message.content.replace(/\n/g, '<br>');

                const timestampDiv = document.createElement('div');
                timestampDiv.className = 'message-timestamp';
                timestampDiv.textContent = message.timestamp;

                messageDiv.appendChild(contentDiv);
                messageDiv.appendChild(timestampDiv);
                messagesContainer.appendChild(messageDiv);
            });

            messagesContainer.scrollTop = messagesContainer.scrollHeight;

            // Update input visibility
            if (gameState.gameEnded) {
                document.getElementById('wishInput').style.display = 'none';
                document.getElementById('resetButton').style.display = 'block';
            } else {
                document.getElementById('wishInput').style.display = 'block';
                document.getElementById('resetButton').style.display = 'none';
            }

            // Update placeholder
            const textarea = document.getElementById('wishText');
            if (gameState.wishesRemaining === 3) {
                textarea.placeholder = "Make your first wish... (e.g., 'I wish for a million dollars')";
            } else if (gameState.wishesRemaining === 2) {
                textarea.placeholder = "Your second wish awaits...";
            } else if (gameState.wishesRemaining === 1) {
                textarea.placeholder = "Choose your final wish carefully...";
            }
        }

        function resetGame() {
            gameState = {
                wishesRemaining: 3,
                messages: [],
                gameStarted: false,
                gameEnded: false,
                apiKey: gameState.apiKey
            };
            initializeGame();
        }

        // Initialize app
        document.addEventListener('DOMContentLoaded', function() {
            console.log('App initializing...');

            // Check if API key exists in localStorage
            const savedApiKey = localStorage.getItem('groqApiKey');
            if (savedApiKey && savedApiKey.startsWith('gsk_')) {
                console.log('Found saved API key, going to main menu');
                appState.apiKey = savedApiKey;
                showMainMenu();
            } else {
                console.log('No API key found, showing setup');
                showSetup();
            }

            // Enter key support for game
            document.getElementById('wishText').addEventListener('keypress', function(e) {
                if (e.key === 'Enter' && !e.shiftKey) {
                    e.preventDefault();
                    makeWish();
                }
            });

            // Enter key support for setup
            document.getElementById('apiKeyInput').addEventListener('keypress', function(e) {
                if (e.key === 'Enter') {
                    e.preventDefault();
                    setupApiKey();
                }
            });
        });

        // Save API key to localStorage when successfully set
        function saveApiKey(apiKey) {
            localStorage.setItem('groqApiKey', apiKey);
        }
    </script>
</body>
</html>
