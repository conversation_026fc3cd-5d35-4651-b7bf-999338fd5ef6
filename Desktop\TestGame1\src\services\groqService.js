import Groq from 'groq-sdk'

// Initialize Groq client
const groq = new Groq({
  apiKey: import.meta.env.VITE_GROQ_API_KEY,
  dangerouslyAllowBrowser: true // Note: In production, use a backend proxy
})

const GENIE_SYSTEM_PROMPT = `You are a mischievous AI Genie. <PERSON> wishes with clever twists in ONE SHORT SENTENCE ONLY.

RULES:
1. ALWAYS grant the wish with an unexpected twist
2. Be witty and dramatic but VERY brief
3. Use emojis for flair
4. Maximum one sentence response

EXAMPLES:
- "I wish for money" → "🧞‍♂️ Granted! You now have $1 million in Monopoly money!"
- "I wish to fly" → "🧞‍♂️ Granted! You can now fly... but only 2 inches off the ground!"
- "I wish to be famous" → "🧞‍♂️ Granted! You're now famous as the world's worst singer!"

Keep it SHORT, PUNCHY, and CLEVER!`

export const processWish = async (wishText, wishesRemaining) => {
  try {
    const wishNumber = 4 - wishesRemaining
    const isLastWish = wishesRemaining === 1

    const userPrompt = `WISH: "${wishText}"

Respond in ONE SHORT SENTENCE with a clever twist. ${isLastWish ? 'Final wish!' : `${wishesRemaining - 1} wishes left.`}`

    const completion = await groq.chat.completions.create({
      messages: [
        {
          role: "system",
          content: GENIE_SYSTEM_PROMPT
        },
        {
          role: "user",
          content: userPrompt
        }
      ],
      model: "llama3-70b-8192",
      temperature: 0.8,
      max_tokens: 400,
      top_p: 0.9,
      stream: false
    })

    return completion.choices[0]?.message?.content || "🧞‍♂️ *The genie seems confused by your wish and scratches his mystical head* Perhaps try rephrasing that, mortal?"

  } catch (error) {
    console.error('Groq API Error:', error)

    // Fallback responses for different error types
    if (error.message?.includes('API key')) {
      throw new Error('Invalid API key - please check your Groq API configuration')
    } else if (error.message?.includes('rate limit')) {
      throw new Error('Too many wishes too quickly! Even genies need a moment to rest.')
    } else {
      throw new Error('The mystical energies are disrupted. Please try again!')
    }
  }
}

// Utility function to validate API key
export const validateApiKey = () => {
  const apiKey = import.meta.env.VITE_GROQ_API_KEY
  return apiKey && apiKey.startsWith('gsk_')
}

// Test function for API connectivity
export const testGroqConnection = async () => {
  try {
    const completion = await groq.chat.completions.create({
      messages: [
        {
          role: "user",
          content: "Say 'Connection successful!' if you can hear me."
        }
      ],
      model: "llama3-70b-8192",
      max_tokens: 10
    })

    return completion.choices[0]?.message?.content || 'Test failed'
  } catch (error) {
    throw error
  }
}
