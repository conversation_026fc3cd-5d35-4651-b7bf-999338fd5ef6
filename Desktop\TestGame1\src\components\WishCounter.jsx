import React from 'react'

const WishCounter = ({ wishesRemaining }) => {
  const renderWishLamps = () => {
    const lamps = []
    for (let i = 0; i < 3; i++) {
      lamps.push(
        <div 
          key={i} 
          className={`wish-lamp ${i < wishesRemaining ? 'active' : 'used'}`}
        >
          🪔
        </div>
      )
    }
    return lamps
  }

  const getStatusText = () => {
    switch (wishesRemaining) {
      case 3:
        return "Three wishes await you"
      case 2:
        return "Two wishes remain"
      case 1:
        return "One final wish"
      case 0:
        return "All wishes granted"
      default:
        return ""
    }
  }

  const getStatusClass = () => {
    if (wishesRemaining === 0) return 'no-wishes'
    if (wishesRemaining === 1) return 'final-wish'
    return 'wishes-available'
  }

  return (
    <div className="wish-counter">
      <div className="wish-lamps">
        {renderWishLamps()}
      </div>
      <div className={`wish-status ${getStatusClass()}`}>
        {getStatusText()}
      </div>
    </div>
  )
}

export default WishCounter
